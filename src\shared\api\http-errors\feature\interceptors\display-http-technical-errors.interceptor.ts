import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
  HttpStatusCode,
} from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { HttpErrorsHelper } from '@gc/shared/api/http-errors/utils';
import { ISAGRI_BUSINESS_HTTP_ERROR_CODE_COLLECTION } from '@gc/shared/api/models';
import { SnackbarService } from '@gc/shared/ui';
import { TranslocoService } from '@jsverse/transloco';
import { catchError, Observable, of, take, tap, throwError } from 'rxjs';

/**
 * TODO https://pirats.atlassian.net/browse/PR-1207
 * HttpError interceptor récupéré de la lib isagri-ng/security.
 * On utilise pas l'interceptor de la lib isagri-ng/security directement car celui-ci utilise le système de modal isagri-ng ui et nous voulons utliser le notre.
 * HttpErrorsInterceptor sera remplacé quand les outils auront fourni leur nouvelle version de lib ; celle ci laissera la possibilité d'implémenter une façade de pilotage de notre modale.
 */

@Injectable()
export class DisplayHttpTechnicalErrorsInterceptor implements HttpInterceptor {
  private _snackbarService = inject(SnackbarService);
  private _translocoService = inject(TranslocoService);

  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((error: unknown) => {
        if (!(error instanceof HttpErrorResponse)) {
          return throwError(() => error);
        }

        if (HttpErrorsHelper.isPasswordExpirationToken(request, error)) {
          return throwError(() => error);
        }

        if (ISAGRI_BUSINESS_HTTP_ERROR_CODE_COLLECTION.includes(error.status)) {
          return throwError(() => error);
        }

        this.showNonBlockingErrorMessage(error);
        return throwError(() => error);
      })
    );
  }

  showNonBlockingErrorMessage(httpErrorResponse: HttpErrorResponse): void {
    this.getErrorMessage(httpErrorResponse)
      .pipe(
        take(1),
        tap((errorMessage: string | null) => {
          if (errorMessage) {
            this._snackbarService.failure({
              message: errorMessage,
            });
          }
        })
      )
      .subscribe();
  }

  getErrorMessage(
    httpErrorResponse: HttpErrorResponse
  ): Observable<string | null> {
    // Pour un cas de deconnexion à internet, l'error n'est pas de type ErrorEvent et n'est donc pas considérée comme une erreur "ClientSide"
    const isClientSideHttpError = httpErrorResponse.error instanceof ErrorEvent;

    if (isClientSideHttpError) {
      return httpErrorResponse.error.message
        ? of(httpErrorResponse.error.message.trim())
        : this._translocoService.selectTranslate<string>(
            'http.client-side-error',
            {},
            'shared/errors'
          );
    }

    if (httpErrorResponse.status === HttpStatusCode.BadRequest) {
      return this._translocoService.selectTranslate<string>(
        'http.bad-request-error',
        {},
        'shared/errors'
      );
    }

    if (httpErrorResponse.status === HttpStatusCode.Unauthorized) {
      return this._translocoService.selectTranslate<string>(
        'http.unauthorized-error',
        {},
        'shared/errors'
      );
    }

    return of(null);
  }
}
